# `WechatCustInfoFacade` 接口测试用例

## 1. 接口概述

- **接口名称:** `WechatCustInfoFacade`
- **接口路径:** `com.howbuy.crm.wechat.client.facade.wechatcustinfo.WechatCustInfoFacade.queryWechatCustInfo(QueryWechatCustInfoRequest)`
- **功能描述:** 根据客户编号（custNo）或微信唯一标识（unionId）查询客户的微信关联信息。

## 2. 依赖数据表范围

为了给接口准备真实有效的测试数据，需要确保以下 **1个** 表中的数据是相互关联且逻辑正确的。

| 表名 | 用途 | 关键字段 | 关联逻辑 |
| :--- | :--- | :--- | :--- |
| `cm_wechat_cust_info` | 存储客户与微信的关联信息 | `cust_no`, `union_id` | 该表为核心数据源，包含查询所需的所有信息。 |

**数据准备核心思路:**
创建一个有效的测试数据，至少需要确保 `cm_wechat_cust_info` 表中存在一条记录，该记录具有唯一的 `cust_no` 和 `union_id`。

## 3. 输入参数 (`QueryWechatCustInfoRequest`)

| 参数名 | 类型 | 是否必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `custNo` | `String` | 否 | 客户的唯一编号。`custNo` 和 `unionId` 至少需要一个。 |
| `unionId` | `String` | 否 | 微信用户的唯一标识。`custNo` 和 `unionId` 至少需要一个。 |

## 4. 输出结果 (`WechatCustInfoVO`)

- **成功:** 返回 `Response.ok(Data)`，其中 `Data` (`WechatCustInfoVO`) 包含客户的微信关联信息，如 `id`, `custNo`, `unionId`, `openId`, `nickName` 等。
- **失败:** 返回带有错误码和错误信息的 `Response` 对象，例如参数校验失败或查询结果为空。

## 5. 测试用例

### 5.1. 正常场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-N-001** | 通过 `custNo` 查询 | 使用一个已知的、存在于 `cm_wechat_cust_info` 表中的 `custNo` 进行查询。 | 数据库中存在 `custNo` 为 `9200000103` 的记录。 | `custNo`: `9200000103` | `{"custNo": "9200000103"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.custNo` 为 `9200000103`。 |
| **TC-N-002** | 通过 `unionId` 查询 | 使用一个已知的、存在于 `cm_wechat_cust_info` 表中的 `unionId` 进行查询。 | 数据库中存在 `unionId` 为 `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ` 的记录。 | `unionId`: `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ` | `{"unionId": "oSx6K1Lt-t2C8n99_kM-cTzSYhAQ"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.unionId` 为 `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ`。 |
| **TC-N-003** | 同时提供 `custNo` 和 `unionId` 查询 | 使用匹配的 `custNo` 和 `unionId` 进行查询，验证查询的准确性。 | 数据库中存在一条记录，其 `custNo` 为 `9200000103` 且 `unionId` 为 `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ`。 | `custNo`: `9200000103`<br>`unionId`: `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ` | `{"custNo": "9200000103", "unionId": "oSx6K1Lt-t2C8n99_kM-cTzSYhAQ"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data` 不为 `null`。<br>3. `Response.data.custNo` 为 `9200000103` 且 `Response.data.unionId` 为 `oSx6K1Lt-t2C8n99_kM-cTzSYhAQ`。 |

### 5.2. 异常及边界场景测试

| 用例ID | 用例标题 | 用例描述 | 前置条件 | 输入参数 | 输入参数json | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-E-001** | `custNo` 和 `unionId` 均为空 | 测试当两个关键查询参数都未提供时，接口是否能正确处理。 | 无 | `custNo`: `null`<br>`unionId`: `null` | `{"custNo": null, "unionId": null}` | 1. `Response.code` 不为 `0`（具体错误码需根据实现确定）。<br>2. `Response.message` 包含参数不能为空的提示信息。 |
| **TC-E-002** | `custNo` 为空字符串 | 测试当 `custNo` 为空字符串时，接口是否能正确处理。 | 无 | `custNo`: `""`<br>`unionId`: `null` | `{"custNo": "", "unionId": null}` | 1. `Response.code` 不为 `0`。<br>2. `Response.message` 包含参数不能为空的提示信息。 |
| **TC-E-003** | `unionId` 为空字符串 | 测试当 `unionId` 为空字符串时，接口是否能正确处理。 | 无 | `custNo`: `null`<br>`unionId`: `""` | `{"custNo": null, "unionId": ""}` | 1. `Response.code` 不为 `0`。<br>2. `Response.message` 包含参数不能为空的提示信息。 |
| **TC-E-004** | 使用不存在的 `custNo` 查询 | 使用一个不存在于 `cm_wechat_cust_info` 表中的 `custNo` 进行查询。 | 数据库中不存在 `custNo` 为 `NON_EXISTENT_CUST` 的记录。 | `custNo`: `NON_EXISTENT_CUST` | `{"custNo": "NON_EXISTENT_CUST"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data` 为 `null`。 |
| **TC-E-005** | 使用不存在的 `unionId` 查询 | 使用一个不存在于 `cm_wechat_cust_info` 表中的 `unionId` 进行查询。 | 数据库中不存在 `unionId` 为 `NON_EXISTENT_UNION_ID` 的记录。 | `unionId`: `NON_EXISTENT_UNION_ID` | `{"unionId": "NON_EXISTENT_UNION_ID"}` | 1. `Response.code` 为 `0`。<br>2. `Response.data` 为 `null`。 |

## 6. 测试数据准备

测试用例使用的数据来源于数据库中的真实数据：

- **测试客户 (ID: 20002398):**
  - `cm_wechat_cust_info`: `HBONE_NO`=`9200000103`, `UNIONID`=`oSx6K1Lt-t2C8n99_kM-cTzSYhAQ`, `NICK_NAME`=`赵木`, `COMPANY_NO`=`1`
  - 该记录用于正常场景测试用例 TC-N-001, TC-N-002, TC-N-003

- **异常测试数据:**
  - 不存在的客户编号: `NON_EXISTENT_CUST`
  - 不存在的微信唯一标识: `NON_EXISTENT_UNION_ID`
  - 用于异常场景测试用例 TC-E-004, TC-E-005
